services:
  postgres-notification:
    image: postgres:16-alpine
    container_name: postgres-notification
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-coupon}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-coupon}
      POSTGRES_DB: ${POSTGRES_DB:-notification_db}
    ports:
      - "5438:5432"
    volumes:
      - postgres-notification-data:/var/lib/postgresql/data
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "pg_isready -U ${POSTGRES_USER:-coupon} -d ${POSTGRES_DB:-notification_db}",
        ]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - coupon-network

  redis-notification:
    image: redis:7-alpine
    container_name: redis-notification
    restart: unless-stopped
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD:-}"]
    ports:
      - "6385:6379"
    volumes:
      - redis-notification-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - coupon-network

  # jaeger:
  #   image: jaegertracing/all-in-one:1.56
  #   container_name: jaeger
  #   restart: unless-stopped
  #   ports:
  #     - "16686:16686"
  #     - "6831:6831/udp"
  #   healthcheck:
  #     test:
  #       - "CMD-SHELL"
  #       - "curl -f http://localhost:16686/health || exit 1"
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 40s
  #   networks:
  #     - coupon-network

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: kafka
    restart: unless-stopped
    environment:
      KAFKA_NODE_ID: 1
      KAFKA_PROCESS_ROLES: broker,controller
      KAFKA_CONTROLLER_QUORUM_VOTERS: 1@kafka:29093
      KAFKA_CONTROLLER_LISTENER_NAMES: CONTROLLER
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:29092,PLAINTEXT_HOST://0.0.0.0:9092,CONTROLLER://0.0.0.0:29093
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT,CONTROLLER:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT

      CLUSTER_ID: MkU3OEVBNTcwNTJENDM2Qk

      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "false"

      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000

      KAFKA_LOG_DIRS: /var/lib/kafka/data
    ports:
      - "9092:9092"
      - "29092:29092"
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - coupon-network
    healthcheck:
      test:
        [
          "CMD",
          "kafka-topics",
          "--bootstrap-server",
          "localhost:9092",
          "--list",
        ]
      interval: 30s
      timeout: 10s
      retries: 5

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    restart: unless-stopped
    depends_on:
      kafka:
        condition: service_healthy
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
    ports:
      - "8090:8080"
    networks:
      - coupon-network

  kafka-topic-init:
    image: confluentinc/cp-kafka:latest
    container_name: kafka-topic-init
    depends_on:
      kafka:
        condition: service_healthy
    volumes:
      - ./scripts/kafka-topic-init.sh:/kafka-topic-init.sh
    command: ["/bin/bash", "/kafka-topic-init.sh"]
    networks:
      - coupon-network
    restart: "no"

  notification-service:
    build:
      context: .
      args:
        GITLAB_USER: ${GITLAB_USER}
        GITLAB_TOKEN: ${GITLAB_TOKEN}
    container_name: notification-service
    image: registry-gitlab.zalopay.vn/phunn4/coupon-notification-service
    depends_on:
      postgres-notification:
        condition: service_healthy
      redis-notification:
        condition: service_healthy
      # jaeger:
      #   condition: service_started
    env_file:
      - .env
    ports:
      - "8086:8080"
      - "50057:50051"
      - "2118:2112"
    restart: unless-stopped
    networks:
      - coupon-network
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:8080/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres-notification-data:
  redis-notification-data:
  kafka-data:

networks:
  coupon-network:
    name: coupon-network
    driver: bridge
